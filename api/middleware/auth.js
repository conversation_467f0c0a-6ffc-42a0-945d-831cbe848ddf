/**
 * Authentication Middleware
 * Handles API authentication using PocketBase admin tokens
 */

import { CONFIG } from '../config/server.js'
import { errors } from './errorHandler.js'
import { SessionService } from '../services/sessionService.js'
import { getDatabaseService } from '../../common/services/databaseService.js'

/**
 * Extract token from request headers
 * @param {Request} req - Express request object
 * @returns {string|null} Token or null if not found
 */
function extractToken(req) {
  const authHeader = req.headers.authorization
  const apiKeyHeader = req.headers[CONFIG.API_KEY_HEADER.toLowerCase()]

  // Check Bearer token in Authorization header
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Check API key in custom header
  if (apiKeyHeader) {
    return apiKeyHeader
  }

  return null
}

/**
 * Validate PocketBase admin token
 * @param {string} token - Token to validate
 * @returns {Promise<Object>} User info if valid
 */
async function validateToken(token) {
  try {
    // For now, implement a simple validation
    // In production, you would validate against PocketBase
    if (!token) {
      throw new Error('No token provided')
    }

    // TODO: Implement actual PocketBase token validation
    try {
      const validation = await getDatabaseService().validateToken(token)
      if (validation.valid) {
        return validation.user
      }
    } catch (error) {
      console.error('Invalid or expired token:', error);
    }

    throw new Error('Invalid token')
  } catch (error) {
    console.error('Token validation error:', error)
    throw errors.UNAUTHORIZED('Invalid authentication token')
  }
}

/**
 * Authentication middleware
 * Validates authentication token and sets user in request
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export async function authenticate(req, res, next) {
  try {
    const token = extractToken(req)

    if (!token) {
      throw errors.UNAUTHORIZED('Authentication token required')
    }

    // Check if token is blacklisted
    const sessionValidation = SessionService.validateSession(token)
    if (!sessionValidation.valid) {
      throw errors.UNAUTHORIZED(`Session invalid: ${sessionValidation.reason}`)
    }

    const user = await validateToken(token)
    req.user = user
    req.session = sessionValidation.session

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * Optional authentication middleware
 * Attempts to authenticate but doesn't fail if no token provided
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export async function optionalAuthenticate(req, res, next) {
  try {
    const token = extractToken(req)

    if (token) {
      // Check session validity for optional auth too
      const sessionValidation = SessionService.validateSession(token)
      if (sessionValidation.valid) {
        const user = await validateToken(token)
        req.user = user
        req.session = sessionValidation.session
      }
    }

    next()
  } catch (error) {
    // For optional auth, we don't fail on auth errors
    next()
  }
}

/**
 * Authorization middleware factory
 * Creates middleware that checks user roles/permissions
 * @param {string|Array} roles - Required roles (admin, user, etc.)
 * @returns {Function} Middleware function
 */
export function authorize(roles = []) {
  return (req, res, next) => {
    if (!req.user) {
      return next(errors.UNAUTHORIZED('Authentication required'))
    }

    // Convert single role to array
    const requiredRoles = Array.isArray(roles) ? roles : [roles]

    // Check if user has required role
    if (requiredRoles.length > 0 && !requiredRoles.includes(req.user.role)) {
      return next(errors.FORBIDDEN('Insufficient permissions'))
    }

    next()
  }
}

/**
 * Admin-only middleware
 * Requires admin role
 */
export const requireAdmin = authorize(['admin'])

/**
 * User or admin middleware
 * Requires user or admin role
 */
export const requireUser = authorize(['user', 'admin'])

/**
 * Create API key middleware
 * Simple API key authentication for programmatic access
 * @param {string} expectedKey - Expected API key
 * @returns {Function} Middleware function
 */
export function createApiKeyAuth(expectedKey) {
  return (req, res, next) => {
    const providedKey = req.headers[CONFIG.API_KEY_HEADER.toLowerCase()]

    if (!providedKey || providedKey !== expectedKey) {
      return next(errors.UNAUTHORIZED('Invalid API key'))
    }

    // Set a default user for API key requests
    req.user = {
      id: 'api-key-user',
      role: 'admin',
      isAdmin: true,
      authenticatedVia: 'api-key'
    }

    next()
  }
}

/**
 * Rate limiting for authentication attempts
 * Prevents brute force attacks
 */
export const authRateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    error: {
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
      message: 'Too many authentication attempts, please try again later.',
      timestamp: new Date().toISOString()
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for valid tokens
    return req.headers.authorization && req.headers.authorization.startsWith('Bearer ')
  }
}
