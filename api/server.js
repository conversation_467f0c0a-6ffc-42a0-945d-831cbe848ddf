/**
 * API Server Entry Point
 * Initializes and starts the Express.js API server with specified routes and middleware
 */

// Load environment variables from .env file
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load .env file from project root
dotenv.config({ path: join(__dirname, '..', '.env') })

import { createApp, CONFIG } from './config/server.js'
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js'
import { taskRoutes } from './routes/tasks.js'
import { projectRoutes } from './routes/projects.js'
import { authRoutes } from './routes/auth.js'

const app = createApp()

// Register API routes
app.use(`${CONFIG.API_PREFIX}/auth`, authRoutes)
app.use(`${CONFIG.API_PREFIX}/tasks`, taskRoutes)
app.use(`${CONFIG.API_PREFIX}/projects`, projectRoutes)

// 404 handler for undefined routes
app.use(notFoundHandler)

// Global error handler
app.use(errorHandler)

// Start API server
app.listen(CONFIG.PORT, () => {
  console.log(`API server running at http://localhost:${CONFIG.PORT}${CONFIG.API_PREFIX}`)
})

