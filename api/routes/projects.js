/**
 * Project Routes
 * Defines route handlers for project management API operations
 */

import express from 'express'
import { validationRules } from '../utils/validation.js'
import { authenticate, requireAdmin } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { ProjectService } from '../services/projectService.js'

// Initialize new Router instance
export const projectRoutes = express.Router()

// Create new project
/**
 * @route POST /api/projects
 */
projectRoutes.post(
  '/',
  authenticate,
  validationRules.createProject,
  asyncHandler(async (req, res) => {
    const projectData = req.body
    const project = await ProjectService.createProject(projectData)
    res.status(201).json({
      success: true,
      data: { project },
      message: 'Project created successfully'
    })
  })
)

// List all projects
/**
 * @route GET /api/projects
 */
projectRoutes.get(
  '/',
  authenticate,
  asyncHandler(async (req, res) => {
    const filters = req.query
    const projects = await ProjectService.listProjects(filters)
    res.status(200).json({
      success: true,
      data: { projects },
      message: 'Projects fetched successfully'
    })
  })
)

// Get specific project by ID
/**
 * @route GET /api/projects/:project_id
 */
projectRoutes.get(
  '/:project_id',
  authenticate,
  validationRules.getProject,
  asyncHandler(async (req, res) => {
    const projectId = req.params.project_id
    const project = await ProjectService.getProjectById(projectId)

    if (!project) {
      return res.status(404).json({ error: 'Project not found' })
    }

    res.status(200).json({
      success: true,
      data: { project },
      message: 'Project fetched successfully'
    })
  })
)

// Update existing project
/**
 * @route PUT /api/projects/:project_id
 */
projectRoutes.put(
  '/:project_id',
  authenticate,
  validationRules.updateProject,
  asyncHandler(async (req, res) => {
    const projectId = req.params.project_id
    const updates = req.body
    const updatedProject = await ProjectService.updateProject(projectId, updates)

    if (!updatedProject) {
      return res.status(404).json({ error: 'Project not updated.' })
    }

    res.status(200).json({
      success: true,
      data: { project: updatedProject },
      message: 'Project updated successfully'
    })
  })
)

// Delete project
/**
 * @route DELETE /api/projects/:project_id
 */
projectRoutes.delete(
  '/:project_id',
  authenticate,
  requireAdmin,
  validationRules.deleteProject,
  asyncHandler(async (req, res) => {
    const projectId = req.params.project_id
    const success = await ProjectService.deleteProject(projectId)

    if (!success) {
      return res.status(404).json({ error: 'Project not deleted.' })
    }

    res.status(200).json({
      success: true,
      message: 'Project deleted successfully'
    })
  })
)

// Get tasks for specific project
/**
 * @route GET /api/projects/:project_id/tasks
 */
projectRoutes.get(
  '/:project_id/tasks',
  authenticate,
  validationRules.getProjectTasks,
  asyncHandler(async (req, res) => {
    const projectId = req.params.project_id
    const filters = req.query
    const tasks = await ProjectService.getProjectTasks(projectId, filters)
    res.status(200).json({
      success: true,
      data: { tasks },
      message: 'Project tasks fetched successfully'
    })
  })
)
