{"numTotalTestSuites": 7, "numPassedTestSuites": 0, "numFailedTestSuites": 7, "numPendingTestSuites": 0, "numTotalTests": 22, "numPassedTests": 4, "numFailedTests": 18, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753449817063, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Tasks Store"], "fullName": "Tasks Store should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 7.180879999999888, "failureMessages": [], "location": {"line": 27, "column": 5}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should add a task successfully", "status": "passed", "title": "should add a task successfully", "duration": 5.944508999999016, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should handle errors when adding a task", "status": "passed", "title": "should handle errors when adding a task", "duration": 1.669501999998829, "failureMessages": [], "location": {"line": 65, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should generate a task_id if not provided", "status": "failed", "title": "should generate a task_id if not provided", "duration": 5.127997999999934, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should use provided task_id if valid", "status": "failed", "title": "should use provided task_id if valid", "duration": 2.495046000000002, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:110:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:110:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:110:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 105, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error for invalid task_id format", "status": "failed", "title": "should throw error for invalid task_id format", "duration": 2.5605820000000676, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:137:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:137:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:137:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error if task_id already exists", "status": "failed", "title": "should throw error if task_id already exists", "duration": 2.838498000000982, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:159:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:159:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:159:16\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 144, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should fetch tasks successfully and populate the store", "status": "passed", "title": "should fetch tasks successfully and populate the store", "duration": 1.6443729999991774, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should set loading to true while fetching", "status": "failed", "title": "should set loading to true while fetching", "duration": 1.6962960000000749, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:190:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:190:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:190:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 188, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should handle errors when fetching tasks", "status": "failed", "title": "should handle errors when fetching tasks", "duration": 3.0158080000001064, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:200:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:200:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:200:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should pass filters to mockDatabaseService.getAllTasks", "status": "failed", "title": "should pass filters to mockDatabaseService.getAllTasks", "duration": 1.6563869999990857, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:213:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:213:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:213:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully", "status": "failed", "title": "should update a task successfully", "duration": 1.8541889999996783, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:230:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:230:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:230:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle task not found when updating", "status": "failed", "title": "should handle task not found when updating", "duration": 1.514563000000635, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:249:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:249:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:249:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 241, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle errors from databaseService when updating a task", "status": "failed", "title": "should handle errors from databaseService when updating a task", "duration": 2.5036939999990864, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:261:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:261:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:261:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 254, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should update a task successfully using PocketBase ID if task_id not found", "duration": 1.6884910000007949, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:284:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:284:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:284:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 276, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully", "status": "failed", "title": "should delete a task successfully", "duration": 1.3772090000002208, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:303:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:303:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:303:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle task not found when deleting", "status": "failed", "title": "should handle task not found when deleting", "duration": 1.5882390000006126, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:321:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:321:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:321:14\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle errors from databaseService when deleting a task", "status": "failed", "title": "should handle errors from databaseService when deleting a task", "duration": 3.159626999999091, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:333:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:333:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:333:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 327, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should delete a task successfully using PocketBase ID if task_id not found", "duration": 1.4440240000003541, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:353:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:353:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:353:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 348, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should get a task by its original ID successfully", "status": "failed", "title": "should get a task by its original ID successfully", "duration": 1.720155999999406, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:371:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:371:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:371:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should return null if task is not found by original ID", "status": "failed", "title": "should return null if task is not found by original ID", "duration": 1.5886009999994712, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:383:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:383:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:383:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should handle errors from databaseService when getting a task by original ID", "status": "failed", "title": "should handle errors from databaseService when getting a task by original ID", "duration": 3.0930610000013985, "failureMessages": ["ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:395:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:395:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: mockDatabaseService is not defined\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:395:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 392, "column": 7}, "meta": {}}], "startTime": 1753449828492, "endTime": 1753449828551.093, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}